<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Input Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 500px;
            margin: 0 auto;
        }
        .file-input {
            margin: 10px 0;
            padding: 10px;
            border: 2px solid #007bff;
            border-radius: 4px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            min-height: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>File Input Test</h1>
        <p>This is a simple test to verify file inputs work in your browser.</p>
        
        <h3>Test 1: Basic File Input</h3>
        <input type="file" id="test1" class="file-input" accept=".csv">
        <div id="result1" class="result">No file selected</div>
        
        <h3>Test 2: File Input with <PERSON><PERSON> Handler</h3>
        <input type="file" id="test2" class="file-input" accept=".csv">
        <div id="result2" class="result">No file selected</div>
        
        <h3>Console Messages</h3>
        <div id="console" class="result">Check browser console (F12) for messages</div>
    </div>

    <script>
        console.log('Test page loaded');
        
        // Test 1: Basic file input
        const test1 = document.getElementById('test1');
        const result1 = document.getElementById('result1');
        
        test1.addEventListener('click', () => {
            console.log('Test 1: File input clicked');
        });
        
        test1.addEventListener('change', (e) => {
            console.log('Test 1: File input changed', e.target.files);
            if (e.target.files.length > 0) {
                result1.textContent = `Selected: ${e.target.files[0].name}`;
            } else {
                result1.textContent = 'No file selected';
            }
        });
        
        // Test 2: File input with more debugging
        const test2 = document.getElementById('test2');
        const result2 = document.getElementById('result2');
        
        test2.addEventListener('click', () => {
            console.log('Test 2: File input clicked');
        });
        
        test2.addEventListener('change', (e) => {
            console.log('Test 2: File input changed', e.target.files);
            if (e.target.files.length > 0) {
                result2.textContent = `Selected: ${e.target.files[0].name}`;
            } else {
                result2.textContent = 'No file selected';
            }
        });
        
        console.log('Test event listeners set up');
    </script>
</body>
</html>
