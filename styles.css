/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.app-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main content layout */
.main-content {
    display: flex;
    gap: 20px;
    flex: 1;
    position: relative;
    min-height: 500px;
}

/* Panel styles */
.panel {
    flex: 1;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.panel-header h2 {
    margin-bottom: 15px;
    color: #495057;
    font-size: 1.3rem;
}

.import-section {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.file-input {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 120px;
}

.file-input:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.file-input::file-selector-button {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    margin-right: 10px;
    transition: all 0.2s ease;
}

.file-input::file-selector-button:hover {
    background: #0056b3;
}

.file-name {
    color: #6c757d;
    font-size: 0.9rem;
    font-style: italic;
    flex: 1;
    min-width: 150px;
}

/* Items container */
.items-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    max-height: 400px;
}

.placeholder {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px 20px;
}

.item {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    user-select: none;
}

.item:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    transform: translateX(2px);
}

.item.selected {
    background: #bbdefb;
    border-color: #1976d2;
    box-shadow: 0 2px 8px rgba(25,118,210,0.3);
}

.item.connected {
    border-left: 4px solid #4caf50;
}

/* Canvas container */
.canvas-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
}

#connectionCanvas {
    width: 100%;
    height: 100%;
    pointer-events: auto;
}

/* Controls */
.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.control-btn {
    background: white;
    color: #495057;
    border: 2px solid #dee2e6;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

/* Status bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255,255,255,0.1);
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    margin-top: 20px;
    font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
        gap: 15px;
    }
    
    .panel {
        min-height: 300px;
    }
    
    .import-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .file-name {
        text-align: center;
        margin-top: 5px;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}

/* Animation for connections */
@keyframes connectionPulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

.connection-line {
    animation: connectionPulse 2s infinite;
}
