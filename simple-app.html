<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Column Mapping Tool - Simple Version</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .main-content {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .panel {
            flex: 1;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .panel h2 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .import-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .file-input {
            margin-bottom: 10px;
            padding: 8px;
            border: 2px solid #007bff;
            border-radius: 4px;
            background: white;
        }
        
        .file-name {
            display: block;
            color: #6c757d;
            font-style: italic;
            font-size: 0.9rem;
        }
        
        .items-container {
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 10px;
        }
        
        .item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 5px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .item:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        
        .item.selected {
            background: #bbdefb;
            border-color: #1976d2;
        }
        
        .placeholder {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 40px 20px;
        }
        
        .status {
            background: rgba(255,255,255,0.1);
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Column Mapping Tool</h1>
            <p>Import CSV files and map columns</p>
        </header>
        
        <div class="main-content">
            <div class="panel">
                <h2>Source Columns</h2>
                <div class="import-section">
                    <input type="file" id="leftFile" class="file-input" accept=".csv">
                    <span id="leftFileName" class="file-name">No file selected</span>
                </div>
                <div id="leftItems" class="items-container">
                    <div class="placeholder">Import a CSV file to see columns</div>
                </div>
            </div>
            
            <div class="panel">
                <h2>Target Columns</h2>
                <div class="import-section">
                    <input type="file" id="rightFile" class="file-input" accept=".csv">
                    <span id="rightFileName" class="file-name">No file selected</span>
                </div>
                <div id="rightItems" class="items-container">
                    <div class="placeholder">Import a CSV file to see columns</div>
                </div>
            </div>
        </div>
        
        <div id="status" class="status">Ready to import CSV files</div>
    </div>

    <script>
        console.log('Simple app loaded');
        
        // Simple CSV parser
        function parseCSV(csvContent) {
            const lines = csvContent.trim().split('\n');
            if (lines.length === 0) return [];
            
            const firstLine = lines[0];
            let columns = [];
            
            // Try different delimiters
            if (firstLine.includes(',')) {
                columns = firstLine.split(',');
            } else if (firstLine.includes('|')) {
                columns = firstLine.split('|');
            } else if (firstLine.includes('\t')) {
                columns = firstLine.split('\t');
            } else {
                columns = firstLine.split(',');
            }
            
            // Clean up column names
            return columns.map(col => col.trim().replace(/^["']|["']$/g, ''));
        }
        
        // Render items
        function renderItems(containerId, items) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            if (items.length === 0) {
                container.innerHTML = '<div class="placeholder">Import a CSV file to see columns</div>';
                return;
            }
            
            items.forEach((item, index) => {
                const itemElement = document.createElement('div');
                itemElement.className = 'item';
                itemElement.textContent = item;
                container.appendChild(itemElement);
            });
        }
        
        // Handle file import
        function handleFileImport(event, side) {
            console.log(`File import for ${side} side`);
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const csvContent = e.target.result;
                const columns = parseCSV(csvContent);
                
                // Update file name display
                const fileNameElement = document.getElementById(side + 'FileName');
                fileNameElement.textContent = file.name;
                
                // Render columns
                renderItems(side + 'Items', columns);
                
                // Update status
                document.getElementById('status').textContent = 
                    `Imported ${columns.length} columns from ${file.name}`;
                
                console.log(`Imported ${columns.length} columns:`, columns);
            };
            
            reader.readAsText(file);
        }
        
        // Set up event listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up event listeners');
            
            const leftFile = document.getElementById('leftFile');
            const rightFile = document.getElementById('rightFile');
            
            if (leftFile) {
                leftFile.addEventListener('click', function() {
                    console.log('Left file input clicked');
                });
                
                leftFile.addEventListener('change', function(e) {
                    console.log('Left file changed');
                    handleFileImport(e, 'left');
                });
                console.log('Left file listeners added');
            }
            
            if (rightFile) {
                rightFile.addEventListener('click', function() {
                    console.log('Right file input clicked');
                });
                
                rightFile.addEventListener('change', function(e) {
                    console.log('Right file changed');
                    handleFileImport(e, 'right');
                });
                console.log('Right file listeners added');
            }
            
            console.log('All event listeners set up successfully');
        });
    </script>
</body>
</html>
