### Product Requirements Document (PRD)

#### 1. **Overview**

The app allows users to draw lines between text items on the left and text items on the right. 
The user interface (UI) will display two lists of text items: one on the left and one on the right. 
Users can visually link corresponding items from each list by drawing lines between them. 
The feature allows a single item on the left to be connected to multiple items on the right, providing flexibility in creating relationships between the items.

The list of items can be populated by allowing the user to specify a csv file (either comma -delimited, pipe-delimited or tab-delimited)
The file should have as its first row the column names. If there are other rows in the file, just ignore them. 
We only care to import the column names into the lists.

#### 2. **Target Audience**

This app is aimed at users who need to visually represent relationships or pairings between items. Typical use cases include:

* **Educational purposes:** Matching terms to definitions, matching questions to answers, etc.
* **Business workflows:** Pairing tasks with responsible team members, associating data points, etc.
* **Interactive learning:** Users can draw connections to test knowledge or understanding of a given set of items.

#### 3. **Objectives**

* Allow users to link multiple items on the left to items on the right.
* Provide an intuitive, interactive UI for easily creating and modifying connections.
* Allow users to visually track and modify connections with ease.
* Make the application scalable for various types of lists and items.
* Ensure an easy-to-use interface that can be adapted to various scenarios.

#### 4. **Features and Functionality**

##### 4.1 **User Interface (UI)**

* **Left Panel:
  ** Displays a list of text items. Each item will be clickable or draggable for interaction.
  ** On top of the list is a button to import a csv file. To the right of the button is a text box to display the name of the file. When the button is clicked, a dialog will appear prompting the user to select the file. When the file is selected, update the text box with the name of the file.
* **Right Panel:
  ** Displays a corresponding list of text items. These items are also clickable or draggable for user interaction.
  ** On top of the list is a button to import a csv file. To the right of the button is a text box to display the name of the file. When the button is clicked, a dialog will appear prompting the user to select the file. When the file is selected, update the text box with the name of the file.
* **Lines/Connections:** Users can click on an item in the left list and drag a line to one or more items on the right. The line(s) should be clearly visible, and the connection should persist until the user decides to remove or modify it.

##### 4.2 **Technology Stack**

* Use HTML, CSS, Javascript 
* Perhaps use HTML5 canvas for drawing lines.


##### 4.2 **User Actions**

* **Drawing Connections:**

  * Click and drag: Clicking on an item on the left, dragging a line to an item on the right.
  * The same line can connect one item on the left to multiple items on the right.

* **Deleting Connections:**

  * Click the line connecting two items to remove the connection.
  * Alternatively, a right-click or a "clear" button can allow users to remove all connections or individual ones.

* **Rearranging Items:**

  * If the lists are scrollable, users should be able to scroll to view more items as needed.
  * The position of the items in the list (left or right) should not affect the ability to create or maintain connections.
* **Highlighting Active Connections:**

  * Active or selected connections should be highlighted for visibility.
  * Upon hover or click, the items being connected should be highlighted to visually guide the user.

##### 4.3 **Line Drawing Specifications**

* **Line Style:** The line drawn should be simple and visually distinguishable (solid line, dashed line, etc.).
* **Multiple Connections:** The same line should be allowed to connect the left item to multiple right items (i.e., a multi-destination connection).
* **Interaction Feedback:** When a line is drawn, the item on the left or right will visually "snap" or show a highlight to indicate that the line is connected.

##### 4.4 **Persistence of Connections**

* All connections should persist as long as the user session is active.
* Connections should be saved and retrievable after the user logs out and logs back in (if applicable).

##### 4.5 **Customization Options**

* Allow users to change line colors or styles to enhance visual clarity.
* Enable users to name or label connections if needed for reference.

#### 5. **Non-Functional Requirements**

* **Performance:** The app should handle lists of text items with a reasonable number of entries, ensuring the UI remains responsive.
* **Compatibility:** The app should be compatible with desktop browsers and mobile browsers for flexibility.
* **Accessibility:** Ensure that the UI can be navigated by keyboard and screen readers for users with disabilities.

#### 6. **User Flows**

1. **Starting the App:**

   * The user opens the app and is presented with two lists: one on the left and one on the right.
   * Text items in each list are clickable.

2. **Drawing a Connection:**

   * The user clicks an item on the left list.
   * The user clicks or drags a line to an item on the right list.
   * The connection between the two items is established visually.

3. **Multiple Connections:**

   * The user can click the same left item and connect it to multiple right items.

4. **Editing and Deleting Connections:**

   * The user can click on a line to delete it.
   * The user can right-click or use a control to delete all connections at once.

5. **Saving State:**

   * The app ensures all connections are saved automatically.

#### 7. **Technical Requirements**

* **Frontend:**

  * JavaScript framework such as React or Vue.js for UI components.
  * CSS Grid/Flexbox for the layout of left and right panels.
  * D3.js or Canvas for drawing lines and handling connections.

* **Backend (optional):**

  * REST API to save user data and session information (if user login and state persistence are required).
  * Database to store user connections and item lists (e.g., PostgreSQL, MongoDB).

#### 8. **User Stories**

1. **As a user,** I want to connect items on the left list to multiple items on the right so I can visualize relationships between them.
2. **As a user,** I want to delete individual connections or reset all connections so I can make adjustments to my choices.
3. **As a user,** I want to be able to scroll through the left and right lists to see all the items without losing the connections I’ve made.
4. **As a user,** I want the app to save my connections so that when I return, they will still be present.

#### 9. **Acceptance Criteria**

* **AC1:** The user can click on an item on the left and drag a line to connect it to one or more items on the right.
* **AC2:** The user can remove a connection by clicking on the line.
* **AC3:** The app saves and displays the connections after the user logs out and logs back in (if applicable).
* **AC4:** The UI is responsive and adapts to different screen sizes.
* **AC5:** The app provides appropriate visual feedback (e.g., highlighted items or lines) during user interaction.

#### 10. **Milestones**

* **M1:** Design and implementation of the UI layout (left and right panels with draggable items).
* **M2:** Implementing the line drawing functionality and connecting multiple items.
* **M3:** Persistence of user interactions and connection saving.
* **M4:** Testing and debugging to ensure smooth user experience.
* **M5:** Final user acceptance testing and deployment.

#### 11. **Future Considerations**

* Allowing users to import/export their connections or share them.
* Enabling additional customization for the appearance of the lines or lists.
* Introducing advanced features such as categorization or grouping for the items on each list.

This document provides the foundational requirements for building an app that enables users to draw and manage lines connecting text items between two lists.
